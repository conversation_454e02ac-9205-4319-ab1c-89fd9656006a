#!/usr/bin/env python3
"""
Comprehensive test script for the new /intervet_new endpoint
Tests CGPA-style scoring system with different weightage configurations
"""

import requests
import json
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
INTERVET_NEW_URL = f"{API_BASE_URL}/intervet_new"

def get_sample_resume_data():
    """Get sample resume data for testing"""
    return {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "location": "San Francisco, CA",
        "summary": "Experienced software engineer with 5+ years in full-stack development",
        "education": [
            {
                "degree": "B.Tech Computer Science",
                "institution": "Stanford University",
                "year": "2018-2022",
                "gpa": "3.8/4.0"
            }
        ],
        "skills": [
            "Python", "JavaScript", "React", "Node.js", "SQL", "AWS", 
            "Docker", "Git", "REST APIs", "Machine Learning"
        ],
        "experience": [
            {
                "company_name": "Tech Corp",
                "role": "Senior Software Engineer",
                "duration": "2022-Present",
                "location": "San Francisco, CA",
                "key_responsibilities": "Developed full-stack applications using React and Node.js, implemented REST APIs, worked with AWS services"
            },
            {
                "company_name": "StartupXYZ",
                "role": "Software Developer",
                "duration": "2020-2022",
                "location": "San Francisco, CA",
                "key_responsibilities": "Built web applications using Python and JavaScript, worked with SQL databases"
            }
        ],
        "projects": [
            {
                "name": "E-commerce Platform",
                "description": "Built a full-stack e-commerce platform using React, Node.js, and MongoDB",
                "technologies_used": ["React", "Node.js", "MongoDB", "Express"]
            }
        ],
        "certifications": [
            {
                "name": "AWS Certified Developer",
                "issuer": "Amazon Web Services",
                "date": "2023"
            }
        ]
    }

def get_sample_jd_data():
    """Get sample job description data for testing"""
    return {
        "job_title": "Full Stack Developer",
        "company_name": "TechCorp Solutions",
        "location": "San Francisco, CA",
        "required_skills": [
            "Python", "JavaScript", "React", "Node.js", "SQL", 
            "REST APIs", "Git", "AWS"
        ],
        "preferred_skills": [
            "Docker", "Kubernetes", "Machine Learning", "TypeScript"
        ],
        "required_experience": "3+ years of experience in full-stack development",
        "education_requirements": [
            "Bachelor's degree in Computer Science or related field"
        ]
    }

def test_default_weights():
    """Test with default weightage configuration"""
    print("\n🧪 Test 1: Default Weights Configuration")
    print("=" * 50)
    
    resume_data = get_sample_resume_data()
    jd_data = get_sample_jd_data()
    
    request_data = {
        "resume_json": resume_data,
        "jd_json": jd_data
        # No weightage provided - should use defaults
    }
    
    try:
        start_time = time.time()
        response = requests.post(INTERVET_NEW_URL, json=request_data, timeout=120)
        processing_time = time.time() - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success! Results:")
            print(f"   📈 Total Score: {result['total_score']:.2f}/10")
            print(f"   🎯 Fit Category: {result['fit_category']}")
            print(f"   💭 Summary: {result['summary']}")
            print(f"   ⚖️  Total Credits Used: {result['total_credits_used']}")
            
            print("\n📋 Field Scores:")
            for field in ['skills_score', 'experience_score', 'education_score', 'certifications_score', 'location_score', 'reliability_score']:
                score_data = result[field]
                print(f"   {field.replace('_score', '').title()}: {score_data['raw_score']:.1f}/10 (weight: {score_data['weight']}, weighted: {score_data['weighted_score']:.1f})")
                print(f"      Rationale: {score_data['rationale']}")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_skills_heavy_weights():
    """Test with skills-heavy weightage configuration"""
    print("\n🧪 Test 2: Skills-Heavy Configuration")
    print("=" * 50)
    
    resume_data = get_sample_resume_data()
    jd_data = get_sample_jd_data()
    
    request_data = {
        "resume_json": resume_data,
        "jd_json": jd_data,
        "weightage": {
            "skills": 5.0,
            "experience": 2.0,
            "education": 1.0,
            "certifications": 1.0,
            "location": 0.5,
            "reliability": 0.5
        }
    }
    
    try:
        start_time = time.time()
        response = requests.post(INTERVET_NEW_URL, json=request_data, timeout=120)
        processing_time = time.time() - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success! Results:")
            print(f"   📈 Total Score: {result['total_score']:.2f}/10")
            print(f"   🎯 Fit Category: {result['fit_category']}")
            print(f"   ⚖️  Total Credits Used: {result['total_credits_used']}")
            
            print("\n📋 Field Scores (Skills-Heavy):")
            for field in ['skills_score', 'experience_score', 'education_score']:
                score_data = result[field]
                print(f"   {field.replace('_score', '').title()}: {score_data['raw_score']:.1f}/10 (weight: {score_data['weight']}, weighted: {score_data['weighted_score']:.1f})")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_experience_heavy_weights():
    """Test with experience-heavy weightage configuration"""
    print("\n🧪 Test 3: Experience-Heavy Configuration")
    print("=" * 50)
    
    resume_data = get_sample_resume_data()
    jd_data = get_sample_jd_data()
    
    request_data = {
        "resume_json": resume_data,
        "jd_json": jd_data,
        "weightage": {
            "skills": 2.0,
            "experience": 5.0,
            "education": 1.5,
            "certifications": 1.0,
            "location": 0.5,
            "reliability": 0.0  # Disable reliability scoring
        }
    }
    
    try:
        start_time = time.time()
        response = requests.post(INTERVET_NEW_URL, json=request_data, timeout=120)
        processing_time = time.time() - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success! Results:")
            print(f"   📈 Total Score: {result['total_score']:.2f}/10")
            print(f"   🎯 Fit Category: {result['fit_category']}")
            print(f"   ⚖️  Total Credits Used: {result['total_credits_used']}")
            
            print("\n📋 Top Field Scores (Experience-Heavy):")
            for field in ['experience_score', 'skills_score', 'education_score']:
                score_data = result[field]
                print(f"   {field.replace('_score', '').title()}: {score_data['raw_score']:.1f}/10 (weight: {score_data['weight']}, weighted: {score_data['weighted_score']:.1f})")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n🧪 Test 4: Edge Cases and Error Handling")
    print("=" * 50)
    
    # Test 1: Missing resume data
    print("\n🔍 Testing missing resume data...")
    try:
        response = requests.post(INTERVET_NEW_URL, json={"jd_json": get_sample_jd_data()}, timeout=30)
        if response.status_code == 400:
            print("✅ Correctly handled missing resume data")
        else:
            print(f"❌ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Invalid weightage values
    print("\n🔍 Testing invalid weightage values...")
    try:
        request_data = {
            "resume_json": get_sample_resume_data(),
            "jd_json": get_sample_jd_data(),
            "weightage": {
                "skills": -1.0,  # Invalid negative weight
                "experience": 15.0,  # Invalid weight > 10
                "education": 2.0,
                "certifications": 1.0,
                "location": 1.0,
                "reliability": 0.5
            }
        }
        response = requests.post(INTERVET_NEW_URL, json=request_data, timeout=30)
        if response.status_code == 422:  # Validation error
            print("✅ Correctly handled invalid weightage values")
        else:
            print(f"❌ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting Comprehensive Tests for /intervet_new Endpoint")
    print("=" * 60)
    
    # Check if API is running
    try:
        response = requests.get(API_BASE_URL, timeout=10)
        if response.status_code != 200:
            print("❌ API server is not running. Please start the server first.")
            return
    except Exception:
        print("❌ Cannot connect to API server. Please start the server first.")
        return
    
    print("✅ API server is running")
    
    # Run tests
    tests = [
        test_default_weights,
        test_skills_heavy_weights,
        test_experience_heavy_weights,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The /intervet_new endpoint is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print("\n📁 Check the 'intervet_new_logs' folder for detailed calculation logs.")

if __name__ == "__main__":
    main()
